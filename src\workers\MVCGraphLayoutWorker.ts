// MVC Graph Layout Web Worker
// This worker offloads graph layout calculations to a separate thread.
// Implements a hybrid approach combining D3 force simulation with deterministic hierarchical positioning.
// Optimized for deep hierarchies and variable node heights based on the legacy graph layout system.

import * as d3 from 'd3';

// Define the node type for the worker - includes hierarchical info
interface ForceNode extends d3.SimulationNodeDatum {
  id: string;
  width: number;
  height: number;
  x?: number;
  y?: number;
  isPinned?: boolean;
  isHorizontallyPinned?: boolean; // Property for partial pinning
  isDragging?: boolean; // Flag to indicate if the node is being dragged
  // Hierarchical relationship fields
  parentId?: string;
  childIds?: string[]; // Used for hierarchical positioning
  depth?: number;      // Calculated depth in the hierarchy
}

// Define the link type
interface ForceLink extends d3.SimulationLinkDatum<ForceNode> {
  source: string | ForceNode; // Can be ID string or node object
  target: string | ForceNode; // Can be ID string or node object
  strength?: number;       // Optional link strength override
}

// Message data structure from main thread
interface LayoutRequest {
  id: string; // Request ID for tracking
  nodes: Array<{
    id: string;       // Hierarchical ID like "0", "0-1", "0-1-2" is fine
    width: number;
    height: number;
    x?: number;       // Initial position (optional)
    y?: number;       // Initial position (optional)
    isPinned?: boolean; // If true, node position is fixed
    isHorizontallyPinned?: boolean; // If true, only horizontal position is fixed
    isDragging?: boolean; // If true, node is currently being dragged
    // Optional: Provide parentId if you want depth calculation for forceY
    parentId?: string;
    // Optional: childIds might be useful for other logic, but not used here
    childIds?: string[];
    // Add relationships object support
    relationships?: {
      parentId?: string;
      childIds?: string[];
    };
  }>;
  edges: Array<{
    source: string;   // Source node ID (e.g., parent's hierarchical ID)
    target: string;   // Target node ID (e.g., child's hierarchical ID)
    strength?: number; // Optional individual link strength
  }>;
  options: { // Simulation tuning options
    iterations?: number;
    alphaDecay?: number;
    velocityDecay?: number;
    jitterProbability?: number; // Small random push to prevent sticking
    // Removed stabilityFactor - let simulation converge naturally
    centerX?: number; // Center of the layout area
    centerY?: number; // Center of the layout area
  };
}

// Helper function to calculate variance (used for dynamic force adjustment and logging)
function calculateVariance(values: number[]): number {
  if (values.length === 0) return 0;
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
}

// Helper function to calculate standard deviation
function calculateStdDev(values: number[]): number {
  return Math.sqrt(calculateVariance(values));
}

// Helper function to validate a number and return a default value if invalid
function validateNumber(value: number | undefined, defaultValue: number, maxValue: number = 100000): number {
  // Check if value is valid and within reasonable limits
  if (value !== undefined && !isNaN(value) && isFinite(value) && value <= maxValue) {
    return value;
  }

  // Log extreme values for debugging
  if (value !== undefined && (value > maxValue || value < -maxValue)) {

  }

  return defaultValue;
}

// Enhanced validation specifically for node positions
function validatePosition(node: ForceNode, centerX: number, centerY: number): void {
  // More detailed logging for debugging
  if (node.x === undefined || isNaN(node.x) || !isFinite(node.x) || Math.abs(node.x) > 100000) {

    // Use a more controlled random offset to avoid large jumps
    node.x = centerX + (Math.random() - 0.5) * 50;
  }

  if (node.y === undefined || isNaN(node.y) || !isFinite(node.y) || Math.abs(node.y) > 100000) {

    // Use a more controlled random offset to avoid large jumps
    node.y = centerY + (Math.random() - 0.5) * 50;
  }

  // Double-check that our fix worked
  if (isNaN(node.x as number) || !isFinite(node.x as number) || node.x === undefined || Math.abs(node.x as number) > 100000) {

    node.x = centerX; // Last resort fallback
  }

  if (isNaN(node.y as number) || !isFinite(node.y as number) || node.y === undefined || Math.abs(node.y as number) > 100000) {

    node.y = centerY; // Last resort fallback
  }

  // Remove any fixed positions that are invalid (this is important!)
  if (node.fx !== undefined && (isNaN(node.fx as number) || !isFinite(node.fx as number) || Math.abs(node.fx as number) > 100000)) {

    node.fx = undefined; // Use undefined instead of null for d3 force layout
  }

  if (node.fy !== undefined && (isNaN(node.fy as number) || !isFinite(node.fy as number) || Math.abs(node.fy as number) > 100000)) {

    node.fy = undefined; // Use undefined instead of null for d3 force layout
  }

  // Final validation using validateNumber to ensure positions are within reasonable limits
  node.x = validateNumber(node.x, centerX, 100000);
  node.y = validateNumber(node.y, centerY, 100000);
}

// Helper function to resolve remaining overlaps after simulation (from legacy)
// Ensures nodes are not overlapping based on their rectangular bounds.
// Enhanced to be more aggressive about preventing overlaps in deeper hierarchies
function resolveRemainingOverlaps(nodes: ForceNode[], nodeDepthMap: Map<string, number>): void {
    // Create a node map for quick lookups
    const nodeMap = new Map<string, ForceNode>();
    nodes.forEach(node => nodeMap.set(node.id, node));
    const baseIterations = 5; // Increased from 3 for better resolution
    const basePadding = 10;   // Increased from 5 for better separation



    // Log node dimensions for debugging

    nodes.forEach(node => {

    });

    for (let k = 0; k < baseIterations; ++k) {
        for (let i = 0; i < nodes.length; ++i) {
            const nodeA = nodes[i];

            // Validate node positions first to avoid NaN propagation
            if (!nodeA || isNaN(nodeA.x ?? NaN) || isNaN(nodeA.y ?? NaN)) {

                continue;
            }

            // Get depth for padding calculation
            const depthA = nodeDepthMap.get(nodeA.id) || 0;

            // Increase padding slightly for deeper nodes
            const depthFactorA = Math.min(1 + (depthA * 0.1), 1.5); // Max 50% increase
            const paddingForA = basePadding * depthFactorA;

            const halfWidthA = nodeA.width / 2 + paddingForA;
            const halfHeightA = nodeA.height / 2 + paddingForA;
            const centerAx = nodeA.x!;
            const centerAy = nodeA.y!;

            // Log node dimensions and padding for debugging
            if (k === 0 && i === 0) {

            }

            for (let j = i + 1; j < nodes.length; ++j) {
                const nodeB = nodes[j];

                // Validate node positions first to avoid NaN propagation
                if (!nodeB || isNaN(nodeB.x ?? NaN) || isNaN(nodeB.y ?? NaN)) {

                    continue;
                }

                // Get depth for padding calculation
                const depthB = nodeDepthMap.get(nodeB.id) || 0;

                // Increase padding slightly for deeper nodes
                const depthFactorB = Math.min(1 + (depthB * 0.1), 1.5); // Max 50% increase
                const paddingForB = basePadding * depthFactorB;

                const halfWidthB = nodeB.width / 2 + paddingForB;
                const halfHeightB = nodeB.height / 2 + paddingForB;
                const centerBx = nodeB.x!;
                const centerBy = nodeB.y!;

                const dx = centerAx - centerBx;
                const dy = centerAy - centerBy;

                const overlapX = (halfWidthA + halfWidthB) - Math.abs(dx);
                const overlapY = (halfHeightA + halfHeightB) - Math.abs(dy);

                // Verify that calculated overlaps are valid numbers
                if (isNaN(overlapX) || isNaN(overlapY)) {

                    continue;
                }

                if (overlapX > 0 && overlapY > 0) {
                    // Nodes overlap
                    const moveFactor = 0.6; // Increased from 0.5 for stronger resolution

                    // Additional factor for parent-child relationships
                    // If these nodes are related, we prefer to separate them vertically
                    const isParentChild = nodeA.parentId === nodeB.id || nodeB.parentId === nodeA.id;

                    // Check if nodes are at the same depth level
                    const depthA = nodeDepthMap.get(nodeA.id) || 0;
                    const depthB = nodeDepthMap.get(nodeB.id) || 0;
                    const sameDepthLevel = depthA === depthB;

                    // Prefer vertical separation for parent-child relationships
                    if (isParentChild) {
                        // Force vertical separation for parent-child with stronger factor
                        const moveY = overlapY * 1.5 * (dy > 0 ? 1 : -1) * moveFactor;

                        // Determine which node is the parent
                        const aIsParent = nodeB.parentId === nodeA.id;

                        // Validate new positions before applying
                        // Modified to allow vertical movement even when horizontally pinned
                        if (!nodeA.isPinned || nodeA.isHorizontallyPinned) {
                            // If A is parent, move it up more; if A is child, move it down more
                            const aFactor = aIsParent ? 0.3 : 0.7;
                            nodeA.y = validateNumber((nodeA.y ?? centerAy) + moveY * aFactor, centerAy);
                        }
                        if (!nodeB.isPinned || nodeB.isHorizontallyPinned) {
                            // If B is parent, move it up more; if B is child, move it down more
                            const bFactor = aIsParent ? 0.7 : 0.3;
                            nodeB.y = validateNumber((nodeB.y ?? centerBy) - moveY * bFactor, centerBy);
                        }
                    }
                    // For nodes at the same level, prefer horizontal separation
                    else if (sameDepthLevel) {
                        // Check if they are siblings (have the same parent)
                        const areSiblings = nodeA.parentId && nodeB.parentId && nodeA.parentId === nodeB.parentId;

                        // Use stronger horizontal separation for siblings
                        const siblingFactor = areSiblings ? 1.5 : 1.2;
                        const moveX = overlapX * (dx > 0 ? 1 : -1) * moveFactor * siblingFactor;



                        // Validate new positions before applying
                        if (!nodeA.isPinned) {
                            nodeA.x = validateNumber((nodeA.x ?? centerAx) + moveX, centerAx);
                        }
                        if (!nodeB.isPinned) {
                            nodeB.x = validateNumber((nodeB.x ?? centerBx) - moveX, centerBx);
                        }

                        // For siblings, also ensure they maintain proper vertical alignment
                        if (areSiblings) {
                            // Get the parent node
                            const parentId = nodeA.parentId;
                            const parentNode = parentId ? nodeMap.get(parentId) : null;

                            if (parentNode) {
                                // Calculate the parent's bottom edge
                                const parentBottomEdge = (parentNode.y || 0) + (parentNode.height / 2);

                                // Calculate dynamic spacing based on parent height
                                const parentHeightFactor = parentNode.height / 356; // Normalize against default height
                                const dynamicSpacing = 250 * Math.max(1, parentHeightFactor);



                                // Ensure both siblings are at the correct vertical position with dynamic spacing
                                const nodeATopTarget = parentBottomEdge + dynamicSpacing;
                                const nodeBTopTarget = parentBottomEdge + dynamicSpacing;

                                const nodeATopCurrent = (nodeA.y || 0) - (nodeA.height / 2);
                                const nodeBTopCurrent = (nodeB.y || 0) - (nodeB.height / 2);

                                // If either sibling is not at the correct vertical position, adjust it
                                if (Math.abs(nodeATopCurrent - nodeATopTarget) > 5 && (!nodeA.isPinned || nodeA.isHorizontallyPinned)) {
                                    const newAY = nodeATopTarget + (nodeA.height / 2);
                                    nodeA.y = validateNumber(newAY, centerAy);
                                }

                                if (Math.abs(nodeBTopCurrent - nodeBTopTarget) > 5 && (!nodeB.isPinned || nodeB.isHorizontallyPinned)) {
                                    const newBY = nodeBTopTarget + (nodeB.height / 2);
                                    nodeB.y = validateNumber(newBY, centerBy);
                                }
                            }
                        }
                    }
                    // Otherwise resolve along the axis with the smaller overlap (standard approach)
                    else if (overlapX < overlapY) {
                        const moveX = overlapX * (dx > 0 ? 1 : -1) * moveFactor;

                        // Validate new positions before applying
                        if (!nodeA.isPinned) {
                            nodeA.x = validateNumber((nodeA.x ?? centerAx) + moveX, centerAx);
                        }
                        if (!nodeB.isPinned) {
                            nodeB.x = validateNumber((nodeB.x ?? centerBx) - moveX, centerBx);
                        }
                    } else {
                        // For vertical separation between nodes at different depths
                        const moveY = overlapY * (dy > 0 ? 1 : -1) * moveFactor;

                        // Determine which node is at a deeper level
                        const aIsDeeper = depthA > depthB;

                        // Validate new positions before applying
                        // Modified to allow vertical movement even when horizontally pinned
                        if (!nodeA.isPinned || nodeA.isHorizontallyPinned) {
                            // If A is deeper, move it down more; otherwise move it up more
                            const aFactor = aIsDeeper ? 0.7 : 0.3;
                            nodeA.y = validateNumber((nodeA.y ?? centerAy) + moveY * aFactor, centerAy);
                        }
                        if (!nodeB.isPinned || nodeB.isHorizontallyPinned) {
                            // If B is deeper, move it down more; otherwise move it up more
                            const bFactor = aIsDeeper ? 0.3 : 0.7;
                            nodeB.y = validateNumber((nodeB.y ?? centerBy) - moveY * bFactor, centerBy);
                        }
                    }
                }
            }
        }
    }


}

// Helper function to enforce consistent depth-based positioning
// Enhanced to better handle deep hierarchies and variable node heights
// Based on the legacy graph layout system's approach
function enforceParentChildSpacing(nodes: ForceNode[], nodeMap: Map<string, ForceNode>): number {


    // --- STEP 1: Calculate node depths and create necessary data structures ---

    // Calculate depth for all nodes based on hierarchical ID
    const depthMap = new Map<string, number>();
    for (const node of nodes) {
        // Count the number of segments in the hierarchical ID
        const idSegments = node.id.split('-');
        const depth = idSegments.length - 1; // Root is 0, first level is 1, etc.
        depthMap.set(node.id, depth);
        // Store depth on the node for easier access
        node.depth = depth;
    }

    // Build parent-child relationships map
    const parentToChildrenMap = new Map<string, ForceNode[]>();
    for (const node of nodes) {
        if (node.parentId) {
            const parent = nodeMap.get(node.parentId);
            if (parent) {
                const children = parentToChildrenMap.get(node.parentId) || [];
                children.push(node);
                parentToChildrenMap.set(node.parentId, children);
            }
        }
    }

    // Group nodes by depth
    const nodesByDepth = new Map<number, ForceNode[]>();
    for (const node of nodes) {
        const depth = node.depth || 0;
        const depthNodes = nodesByDepth.get(depth) || [];
        depthNodes.push(node);
        nodesByDepth.set(depth, depthNodes);
    }

    // --- STEP 2: Calculate dimensions and bottom edges for all nodes ---

    // Validate and calculate bottom edge for each node
    const nodeBottomEdgeMap = new Map<string, number>();
    for (const node of nodes) {
        // Validate node height
        if (isNaN(node.height) || node.height <= 0 || node.height > 10000) {

            node.height = 356; // Default height
        }

        // Validate node width
        if (isNaN(node.width) || node.width <= 0 || node.width > 10000) {

            node.width = 336; // Default width
        }

        // Calculate bottom edge position (y coordinate of the bottom edge)
        const bottomEdge = (node.y || 0) + (node.height / 2);
        nodeBottomEdgeMap.set(node.id, bottomEdge);


    }

    // Calculate the lowest bottom edge for each depth level
    const depthLowestBottomEdgeMap = new Map<number, number>();
    nodesByDepth.forEach((depthNodes, depth) => {
        // Find the lowest bottom edge at this depth
        const bottomEdges = depthNodes.map(node => nodeBottomEdgeMap.get(node.id) || 0);
        const lowestBottomEdge = Math.max(...bottomEdges);
        depthLowestBottomEdgeMap.set(depth, lowestBottomEdge);

    });

    // --- STEP 3: Calculate vertical positions for each depth level based on parent bottom edges ---

    // Initialize vertical positioning constants
    const ROOT_VERTICAL_POSITION = -400; // Position root nodes higher up
    // Use dynamic spacing based on average node heights
    const avgNodeHeight = nodes.reduce((sum, node) => sum + node.height, 0) / nodes.length;
    const BASE_VERTICAL_SPACING = Math.max(150, Math.min(350, avgNodeHeight * 0.7));



    // Calculate the vertical position for each depth level
    const depthVerticalPositions = new Map<number, number>();

    // Set position for root nodes (depth 0)
    depthVerticalPositions.set(0, ROOT_VERTICAL_POSITION);

    // Calculate positions for all other depths based on the lowest bottom edge of the previous depth
    const maxDepth = Math.max(...Array.from(depthMap.values()), 0);

    for (let depth = 1; depth <= maxDepth; depth++) {
        const previousDepth = depth - 1;
        const previousLowestBottomEdge = depthLowestBottomEdgeMap.get(previousDepth) || 0;

        // Get nodes at this depth
        const nodesAtDepth = nodesByDepth.get(depth) || [];
        if (nodesAtDepth.length === 0) continue;

        // Calculate average height of nodes at this depth
        const avgHeight = nodesAtDepth.reduce((sum, node) => sum + node.height, 0) / nodesAtDepth.length;

        // Calculate dynamic spacing based on the average height of nodes at the previous depth
        const previousDepthNodes = nodesByDepth.get(previousDepth) || [];
        const previousAvgHeight = previousDepthNodes.length > 0
            ? previousDepthNodes.reduce((sum, node) => sum + node.height, 0) / previousDepthNodes.length
            : 356; // Default if no nodes

        // Scale spacing based on the height of the previous level's nodes
        const heightFactor = previousAvgHeight / 356; // Normalize against default height
        const dynamicSpacing = BASE_VERTICAL_SPACING * Math.max(1, heightFactor);

        // Calculate center position for nodes at this depth
        // Position is based on the lowest bottom edge of any node at the previous depth
        // plus dynamic spacing plus half the average height of nodes at this depth
        const position = previousLowestBottomEdge + dynamicSpacing + (avgHeight / 2);
        depthVerticalPositions.set(depth, position);


    }

    // --- STEP 4: Apply parent-based vertical positioning to all nodes ---

    let adjustmentsMade = 0;

    // Process nodes by depth level (top to bottom)
    for (let depth = 0; depth <= maxDepth; depth++) {
        const nodesAtDepth = nodesByDepth.get(depth) || [];

        // For root nodes (depth 0), use the fixed vertical position
        if (depth === 0) {
            const verticalPosition = depthVerticalPositions.get(depth);
            if (verticalPosition === undefined) continue;

            for (const node of nodesAtDepth) {
                // Check if we need to adjust the position (allow small tolerance of 5px)
                if (Math.abs((node.y || 0) - verticalPosition) > 5) {
                    // Apply the adjustment if the node isn't fully pinned
                    if (!node.isPinned || node.isHorizontallyPinned) {
                        node.y = verticalPosition;

                        // Update the bottom edge map with the new position
                        const newBottomEdge = verticalPosition + (node.height / 2);
                        nodeBottomEdgeMap.set(node.id, newBottomEdge);

                        adjustmentsMade++;
                    }
                }
            }
        }
        // For non-root nodes, position based on parent's bottom edge
        else {
            // Sort nodes at this depth by their parent and horizontal position
            nodesAtDepth.sort((a, b) => {
                // First sort by parent ID
                if (a.parentId !== b.parentId) {
                    return (a.parentId || '').localeCompare(b.parentId || '');
                }
                // Then by horizontal position
                return (a.x || 0) - (b.x || 0);
            });

            for (const node of nodesAtDepth) {
                // Skip nodes without a parent
                if (!node.parentId) continue;

                // Get the parent node
                const parent = nodeMap.get(node.parentId);
                if (!parent) continue;

                // Get the parent's bottom edge
                const parentBottomEdge = nodeBottomEdgeMap.get(parent.id);
                if (parentBottomEdge === undefined) continue;

                // Calculate dynamic spacing based on parent height
                const heightFactor = parent.height / 356; // Normalize against default height
                const dynamicSpacing = BASE_VERTICAL_SPACING * Math.max(1, heightFactor);

                // Calculate position based on parent's bottom edge plus spacing plus half node height
                const verticalPosition = parentBottomEdge + dynamicSpacing + (node.height / 2);



                // Check if we need to adjust the position (allow small tolerance of 5px)
                if (Math.abs((node.y || 0) - verticalPosition) > 5) {
                    // Validate the position is reasonable
                    if (isNaN(verticalPosition) || !isFinite(verticalPosition) || Math.abs(verticalPosition) > 100000) {

                        continue;
                    }

                    // Apply the adjustment if the node isn't fully pinned
                    if (!node.isPinned || node.isHorizontallyPinned) {
                        // CRITICAL: Directly set the node's y position
                        node.y = verticalPosition;

                        // Update the bottom edge map with the new position
                        const newBottomEdge = verticalPosition + (node.height / 2);
                        nodeBottomEdgeMap.set(node.id, newBottomEdge);

                        adjustmentsMade++;
                    }
                }
            }
        }
    }

    // --- STEP 5: Apply horizontal positioning for siblings ---

    // Process each parent's children to position siblings properly
    parentToChildrenMap.forEach((children, parentId) => {
        if (children.length <= 1) return; // Skip if only one child

        const parent = nodeMap.get(parentId);
        if (!parent || parent.x === undefined) return;

        const parentCenterX = parent.x;
        const HORIZONTAL_SPACING = 150; // Space between siblings

        // Calculate total width needed for all siblings
        const totalWidth = children.reduce((sum, child) => sum + child.width, 0) +
                          (children.length - 1) * HORIZONTAL_SPACING;

        // Position siblings centered under their parent
        let currentX = parentCenterX - (totalWidth / 2);

        // Sort children by their current x position to maintain relative ordering
        const sortedChildren = [...children].sort((a, b) => (a.x || 0) - (b.x || 0));

        for (const child of sortedChildren) {
            // Skip pinned nodes
            if (child.isPinned) {
                currentX += child.width + HORIZONTAL_SPACING;
                continue;
            }

            // Calculate new position (center of the node)
            const newX = currentX + (child.width / 2);

            // Apply position if it's significantly different
            if (Math.abs((child.x || 0) - newX) > 5) {
                child.x = newX;
                adjustmentsMade++;
            }

            // Move to next position
            currentX += child.width + HORIZONTAL_SPACING;
        }
    });


    return adjustmentsMade;
}

// --- Main Processing Function ---
function processLayoutRequest(data: LayoutRequest): void {
  const { id, nodes: inputNodes, edges, options } = data;

  try {
    // --- Simulation Parameters (Based on Stable Legacy Worker) ---
    const iterations = validateNumber(options.iterations, 300); // Recommended minimum iterations
    const alphaDecay = validateNumber(options.alphaDecay, 0.0228); // Standard D3 alpha decay
    const velocityDecay = validateNumber(options.velocityDecay, 0.4); // Standard D3 friction
    const jitterProbability = validateNumber(options.jitterProbability, 0.02); // Small chance for jitter
    const centerX = validateNumber(options.centerX, 0);
    const centerY = validateNumber(options.centerY, 0);

    // Track if we have any dragging nodes to send position updates
    let hasDraggingNodes = false;

    // Check for dragging nodes
    const draggingNodes = inputNodes.filter(node => node.isDragging);
    if (draggingNodes.length > 0) {
      console.log(`[DragProtocol] 70. Worker: Found ${draggingNodes.length} dragging nodes`);
      hasDraggingNodes = true;

      draggingNodes.forEach(node => {
        console.log(`[DragProtocol] 71. Worker: Node ${node.id} is being dragged at position (${node.x}, ${node.y})`);
      });
    } else {
      console.log(`[DragProtocol] 70. Worker: No dragging nodes found`);
    }



    // --- Prepare Nodes ---


    // Calculate statistics about node dimensions for better layout decisions
    const nodeWidths = inputNodes.map(node => node.width).filter(w => !isNaN(w) && w > 0);
    const nodeHeights = inputNodes.map(node => node.height).filter(h => !isNaN(h) && h > 0);

    const avgWidth = nodeWidths.reduce((sum, w) => sum + w, 0) / (nodeWidths.length || 1);
    const avgHeight = nodeHeights.reduce((sum, h) => sum + h, 0) / (nodeHeights.length || 1);
    const widthStdDev = calculateStdDev(nodeWidths);
    const heightStdDev = calculateStdDev(nodeHeights);



    // Log individual node dimensions
    inputNodes.forEach(node => {

    });

    const forceNodes: ForceNode[] = inputNodes.map(node => {
      // Default sizes for width and height if they're missing or invalid
      const defaultWidth = 100;
      const defaultHeight = 50;
      const width = validateNumber(node.width, defaultWidth);
      const height = validateNumber(node.height, defaultHeight);

      // Log if dimensions were changed from input
      if (width !== node.width || height !== node.height) {
        console.log(`[DragProtocol] 63. Worker: Fixed invalid dimensions for node ${node.id}: ${node.width}x${node.height} -> ${width}x${height}`);
      }

      // Log dragging status for debugging
      if (node.isDragging) {
        console.log(`[DragProtocol] 64. Worker: Node ${node.id} is marked as dragging`);
      }

      // Default positions (centered) if they're missing or invalid
      const x = validateNumber(node.x, centerX + (Math.random() - 0.5) * 100);
      const y = validateNumber(node.y, centerY + (Math.random() - 0.5) * 100);

      // Handle dragging nodes and manually positioned nodes
      // If a node is being dragged, always pin its position
      // For other nodes, respect the isPinned flag (for backward compatibility)
      const isDragging = !!node.isDragging;
      const isPinned = !!node.isPinned;
      const isHorizontallyPinned = !!node.isHorizontallyPinned;

      console.log(`[DragProtocol] 65. Worker: Processing node ${node.id}: isDragging=${isDragging}, isPinned=${isPinned}, isHorizontallyPinned=${isHorizontallyPinned}`);

      let fx = (isDragging || isPinned) ? x : undefined;
      let fy = (isDragging || (isPinned && !isHorizontallyPinned)) ? y : undefined;

      // Log dragging status for debugging
      if (isDragging) {
        console.log(`[DragProtocol] 66. Worker: Pinning dragged node ${node.id} at position (${x}, ${y})`);

        // Send an immediate position update to the main thread
        // This ensures the node position is updated in the node store
        self.postMessage({
          type: 'positionUpdate',
          id: 'drag-' + Date.now(),
          nodes: [{
            id: node.id,
            x: x,
            y: y,
            isDragging: true
          }]
        });

        console.log(`[DragProtocol] 67. Worker: Sent immediate position update for node ${node.id} at position (${x}, ${y})`);
      } else if (isPinned) {
        console.log(`[DragProtocol] 68. Worker: Node ${node.id} is pinned at position (${x}, ${y})`);
      }

      // Additional validation for fixed positions
      if (fx !== undefined && (isNaN(fx) || !isFinite(fx))) {
        console.log(`[DragProtocol] 69. Worker: Invalid fx value for node ${node.id}, clearing it`);
        fx = undefined;
      }

      if (fy !== undefined && (isNaN(fy) || !isFinite(fy))) {
        console.log(`[DragProtocol] 70. Worker: Invalid fy value for node ${node.id}, clearing it`);
        fy = undefined;
      }

      return {
        id: node.id,
        width: width,
        height: height,
        x: x,
        y: y,
        fx: fx, // Fix position if pinned, but only if valid
        fy: fy, // Fix position if pinned, but only if valid
        isPinned: node.isPinned,
        isHorizontallyPinned: node.isHorizontallyPinned,
        // Extract parentId either directly or from relationships object
        parentId: node.parentId || (node.relationships?.parentId)
        // childIds are not used by this layout but kept on the node object
      };
    });

    // Create a node map for quick lookups (will be used throughout the process)
    const nodeMap = new Map<string, ForceNode>();
    forceNodes.forEach(node => nodeMap.set(node.id, node));

    // --- Prepare Links ---
    // Filter out edges where source or target node doesn't exist
    const validEdges = edges.filter(edge => {
      const sourceExists = nodeMap.has(edge.source);
      const targetExists = nodeMap.has(edge.target);

      if (!sourceExists || !targetExists) {
        console.log(`[MVCGraphWorker] Filtering out edge ${edge.source}-${edge.target} - source exists: ${sourceExists}, target exists: ${targetExists}`);
        return false;
      }

      return true;
    });

    console.log(`[MVCGraphWorker] Filtered out ${edges.length - validEdges.length} edges with missing nodes`);

    // Ensure source/target refer to IDs D3 can find
    const links: ForceLink[] = validEdges.map(edge => ({
      source: edge.source, // Should match a node ID
      target: edge.target, // Should match a node ID
      strength: edge.strength // Use provided strength or let forceLink decide
    }));

    // --- Log validation information ---
    const invalidNodes = forceNodes.filter(node =>
      isNaN(node.x!) || isNaN(node.y!) ||
      isNaN(node.width) || isNaN(node.height) ||
      node.width <= 0 || node.height <= 0
    );

    if (invalidNodes.length > 0) {
      console.warn(`[MVCGraphWorker] Found ${invalidNodes.length} nodes with invalid properties, fixed with defaults:`,
        invalidNodes.map(n => `${n.id}: x=${n.x}, y=${n.y}, w=${n.width}, h=${n.height}`));

      // Fix invalid nodes immediately
      invalidNodes.forEach(node => {
        validatePosition(node, centerX, centerY);
        node.width = validateNumber(node.width, 100);
        node.height = validateNumber(node.height, 50);
      });
    }

    // --- Validate all positions again for safety ---
    forceNodes.forEach(node => validatePosition(node, centerX, centerY));

    // --- Calculate Depth (Only if parentId is consistently provided) ---
    // This is a critical step for proper hierarchical layout
    const nodeDepthMap = new Map<string, number>();
    let hierarchyAvailable = false;

    // Check if at least one node has a parentId to attempt depth calculation
    if (inputNodes.some(n => n.parentId !== undefined || n.relationships?.parentId !== undefined)) {
        hierarchyAvailable = true;

        // First pass: identify root nodes (depth 0)
        const inputNodeIds = new Set(inputNodes.map(n => n.id));
        forceNodes.forEach(node => {
            if (!node.parentId || !inputNodeIds.has(node.parentId)) {
                nodeDepthMap.set(node.id, 0);
                // Store depth on the node object for easier access
                node.depth = 0;
            }
        });

        // Second pass: iteratively calculate depth for all other nodes
        // This handles complex hierarchies with multiple levels
        let changed = true;
        let depthIterations = 0;
        const maxDepthIterations = forceNodes.length * 2; // Increased safety limit

        while (changed && depthIterations < maxDepthIterations) {
            changed = false;
            depthIterations++;

            forceNodes.forEach(node => {
                if (node.parentId && nodeDepthMap.has(node.parentId)) {
                    const parentDepth = nodeDepthMap.get(node.parentId)!;
                    const currentDepth = nodeDepthMap.get(node.id);

                    // Update depth if not set or needs to be increased
                    if (currentDepth === undefined || currentDepth <= parentDepth) {
                        const newDepth = parentDepth + 1;
                        nodeDepthMap.set(node.id, newDepth);
                        // Store depth on the node object
                        node.depth = newDepth;
                        changed = true;
                    }
                }
            });
        }

        // Log depth calculation results

        const depthCounts = new Map<number, number>();
        forceNodes.forEach(node => {
            const depth = node.depth || 0;
            depthCounts.set(depth, (depthCounts.get(depth) || 0) + 1);
        });

        // Log depth distribution


    }

    // --- Create a simple jitter function to apply random nudges when needed ---
    const jitter = () => {
        for (const node of forceNodes) {
            if (!node.isPinned && Math.random() < jitterProbability) {
                // Apply small random offset
                node.x = (node.x || 0) + (Math.random() - 0.5) * 5;
                node.y = (node.y || 0) + (Math.random() - 0.5) * 5;

                // Always validate after jittering
                validatePosition(node, centerX, centerY);
            }
        }
    };

    // --- Setup D3 Force Simulation ---
    const simulation = d3.forceSimulation<ForceNode>(forceNodes)
        .alpha(1) // Start with full energy
        .alphaDecay(alphaDecay)
        .velocityDecay(velocityDecay)
        .stop(); // Stop until we're ready to run

    // Add a tick handler to send position updates for dragged nodes
    if (hasDraggingNodes) {
      console.log(`[DragProtocol] 72. Worker: Adding tick handler to send position updates for dragged nodes`);

      // Add a tick handler to the simulation
      simulation.on('tick', () => {
        // Only send updates if we have dragging nodes
        if (hasDraggingNodes) {
          // Find all dragging nodes
          const draggedNodes = forceNodes.filter(node => node.isDragging);

          // Only send updates if we have dragging nodes
          if (draggedNodes.length > 0) {
            console.log(`[DragProtocol] 80. Worker sending position updates for ${draggedNodes.length} dragged nodes`);

            // Send position updates to the main thread
            self.postMessage({
              type: 'positionUpdate',
              id: id,
              nodes: draggedNodes.map(node => ({
                id: node.id,
                x: node.x,
                y: node.y,
                isDragging: true
              }))
            });
          }
        }
      });
    }

    // --- Prepare for hierarchical layout ---
    // Build parent-child relationships map
    const parentToChildrenMap = new Map<string, ForceNode[]>();
    forceNodes.forEach(node => {
        if (node.parentId) {
            const parent = nodeMap.get(node.parentId);
            if (parent) {
                const children = parentToChildrenMap.get(node.parentId) || [];
                children.push(node);
                parentToChildrenMap.set(node.parentId, children);
            }
        }
    });

    // Clear and recalculate depth for all nodes based on hierarchical ID
    nodeDepthMap.clear();

    // Calculate depth based on hierarchical ID (number of segments)
    forceNodes.forEach(node => {
        // Count the number of segments in the hierarchical ID
        const idSegments = node.id.split('-');
        const depth = idSegments.length - 1; // Root is 0, first level is 1, etc.
        nodeDepthMap.set(node.id, depth);


    });

    // Find and log root nodes (nodes with depth 0)
    const rootNodes = forceNodes.filter(node => (nodeDepthMap.get(node.id) || 0) === 0);


    // Group nodes by depth for layout calculations
    const depthNodeCounts = new Map<number, number>();
    const depthNodeLists = new Map<number, ForceNode[]>();

    // Group nodes by depth
    forceNodes.forEach(node => {
        const depth = nodeDepthMap.get(node.id) || 0;

        // Initialize or increment the count for this depth
        const currentCount = depthNodeCounts.get(depth) || 0;
        depthNodeCounts.set(depth, currentCount + 1);

        // Add node to the list for this depth
        const nodeList = depthNodeLists.get(depth) || [];
        nodeList.push(node);
        depthNodeLists.set(depth, nodeList);
    });



    // Group nodes by parent for sibling calculations
    const parentToSiblingsMap = new Map<string, ForceNode[]>();
    forceNodes.forEach(node => {
        if (node.parentId) {
            const siblings = parentToSiblingsMap.get(node.parentId) || [];
            siblings.push(node);
            parentToSiblingsMap.set(node.parentId, siblings);
        }
    });

    // --- STEP 1: Deterministic hierarchical pre-positioning ---
    // This is a critical step for handling deep hierarchies properly


    // Apply the enhanced parent-child spacing algorithm to set initial positions
    // This function handles both vertical and horizontal positioning
    enforceParentChildSpacing(forceNodes, nodeMap);

    // --- STEP 2: Verify and fix any unpositioned nodes ---
    let unpositionedNodes = 0;
    forceNodes.forEach(node => {
        if (node.x === undefined || node.y === undefined ||
            isNaN(node.x) || isNaN(node.y)) {
            unpositionedNodes++;

            // For root nodes, position at origin with slight offset
            if (!node.parentId) {
                node.x = (Math.random() - 0.5) * 100;
                node.y = -400 + (Math.random() - 0.5) * 50;
            }
            // For child nodes, position relative to parent if possible
            else {
                const parent = nodeMap.get(node.parentId);
                if (parent && parent.x !== undefined && parent.y !== undefined) {
                    // Position below parent with slight horizontal offset
                    node.x = parent.x + (Math.random() - 0.5) * 100;

                    // Calculate parent's bottom edge
                    const parentBottomEdge = (parent.y || 0) + (parent.height / 2);

                    // Calculate dynamic spacing based on parent height
                    const parentHeightFactor = parent.height / 356; // Normalize against default height
                    const dynamicSpacing = 250 * Math.max(1, parentHeightFactor);

                    // Position based on parent's bottom edge plus dynamic spacing
                    node.y = parentBottomEdge + dynamicSpacing + (node.height / 2);


                } else {
                    // Fallback to default position with depth-based offset
                    const depth = node.depth || 0;
                    node.x = (Math.random() - 0.5) * 100;
                    node.y = -400 + (depth * 250);
                }
            }


        }
    });

    if (unpositionedNodes > 0) {

        // Run parent-child spacing again to ensure proper positioning
        enforceParentChildSpacing(forceNodes, nodeMap);
    }

    // --- STEP 3: Log the pre-positioned node layout ---


    // Group nodes by depth for logging
    const nodesByDepthForLogging = new Map<number, ForceNode[]>();
    forceNodes.forEach(node => {
        const depth = node.depth || 0;
        const depthNodes = nodesByDepthForLogging.get(depth) || [];
        depthNodes.push(node);
        nodesByDepthForLogging.set(depth, depthNodes);
    });

    // Log nodes by depth
    nodesByDepthForLogging.forEach((nodesAtDepth, depth) => {

        nodesAtDepth.forEach(node => {

        });
    });

    // --- Add Forces ---

    // 1. Link Force (for connecting related nodes)
    const forceLink = d3.forceLink<ForceNode, ForceLink>(links)
        .id(d => d.id)  // Ensure we use the node's id property
        .distance(link => {
            // Default link distance based on connected nodes' sizes
            const source = typeof link.source === 'object' ? link.source :
                    forceNodes.find(n => n.id === link.source);
            const target = typeof link.target === 'object' ? link.target :
                    forceNodes.find(n => n.id === link.target);

            // If source or target node doesn't exist, log and return default
            if (!source || !target) {
                console.log(`[MVCGraphWorker] Link distance calculation: Missing node - source: ${link.source}, target: ${link.target}`);
                return 150; // Default fallback
            }

            // Check if this is a parent-child relationship
            const isParentChild = target.parentId === source.id || source.parentId === target.id;

            if (isParentChild) {
                // For parent-child links, use the height of the parent node plus spacing
                // This ensures children are positioned based on parent's height
                const parent = target.parentId === source.id ? source : target;
                const child = target.parentId === source.id ? target : source;

                // Use parent's height plus fixed spacing
                // Increased from 180px to 250px to ensure more space between nodes
                // Log the spacing calculation for debugging
                const spacing = parent.height + 250;

                return spacing;
            }

            // For other links, base distance on the average size of the two nodes plus padding
            return (source.width + source.height + target.width + target.height) / 4 + 40;
        })
        .strength(link => {
            // Use link's specified strength if available
            if (link.strength !== undefined) return link.strength;

            // Otherwise calculate strength based on connected nodes' attributes
            const source = typeof link.source === 'object' ? link.source :
                    forceNodes.find(n => n.id === link.source);
            const target = typeof link.target === 'object' ? link.target :
                    forceNodes.find(n => n.id === link.target);

            // If source or target node doesn't exist, log and return default
            if (!source || !target) {
                console.log(`[MVCGraphWorker] Link strength calculation: Missing node - source: ${link.source}, target: ${link.target}`);
                return 0.7; // Default fallback
            }

            // Strengthen connections between parent-child (hierarchical)
            if (target.parentId === source.id || source.parentId === target.id) {
                return 0.9; // Stronger for parent-child to ensure proper positioning
            }

            return 0.7; // Standard strength
        });

    simulation.force('link', forceLink);

    // 2. Charge Force (for repulsion between nodes)
    const forceCharge = d3.forceManyBody<ForceNode>()
        .strength(node => {
            // Base charge on node size
            const baseCharge = -50; // Increased base strength
            const sizeMultiplier = (node.width + node.height) / 100;
            return baseCharge * Math.max(1, sizeMultiplier);
        })
        .distanceMin(10)  // Minimum distance for charge effect
        .distanceMax(250); // Maximum distance for charge effect

    simulation.force('charge', forceCharge);

    // 3. Collision Force (to prevent node overlap)
    const forceCollide = d3.forceCollide<ForceNode>()
        .radius(node => {
            // Use node dimensions and add padding
            return Math.max(node.width, node.height) / 2 + 5;
        })
        .strength(0.85); // Make collision fairly strong to reduce overlaps

    simulation.force('collide', forceCollide);

    // 4. Y Force (Optional: for hierarchical layouts)
    if (hierarchyAvailable) {
        // Create a map to store the bottom edge position of each node by depth level
        const depthBottomEdgeMap = new Map<number, number>();

        // Initialize with centerY for depth 0
        depthBottomEdgeMap.set(0, centerY);

        // Calculate bottom edge positions for each depth level
        forceNodes.forEach(node => {
            const depth = nodeDepthMap.get(node.id) || 0;
            const nodeBottomEdge = (node.y || 0) + (node.height / 2);

            // Update the maximum bottom edge for this depth level
            const currentMax = depthBottomEdgeMap.get(depth) || centerY;
            if (nodeBottomEdge > currentMax) {
                depthBottomEdgeMap.set(depth, nodeBottomEdge);
            }
        });

        // Create a forceY that places nodes at levels according to hierarchy depth
        // Position based on parent-child relationships
        simulation.force('y', d3.forceY<ForceNode>().y(node => {
            const depth = node.depth || 0;

            // Calculate vertical position based on parent bottom edge
            // For root nodes, use fixed position
            if (depth === 0) {
                return -400; // Root position
            }

            // For child nodes, find parent and position based on parent's bottom edge
            const parentId = node.parentId;
            if (!parentId) {
                // Fallback to depth-based positioning if no parent

                return -400 + (depth * 250);
            }

            // Get parent node
            const parentNode = nodeMap.get(parentId);
            if (!parentNode || parentNode.y === undefined) {
                // Fallback to depth-based positioning if parent not found

                return -400 + (depth * 250);
            }

            // Calculate parent's bottom edge
            const parentBottomEdge = (parentNode.y || 0) + (parentNode.height / 2);

            // Calculate dynamic spacing based on parent height
            const heightFactor = parentNode.height / 356; // Normalize against default height
            const dynamicSpacing = 250 * Math.max(1, heightFactor);

            // Calculate position based on parent's bottom edge plus spacing plus half node height
            const verticalPosition = parentBottomEdge + dynamicSpacing + (node.height / 2);



            // CRITICAL: Always directly set the node's y position to ensure it takes effect immediately
            // This is essential to prevent depth-based positioning from overriding our calculations
            node.y = verticalPosition;

            // If this is a dynamic resize event, ensure vertical movement is allowed
            if (node.isHorizontallyPinned) {
                node.fy = undefined; // Ensure vertical movement is allowed
            }

            return verticalPosition;
        }).strength(0.99)); // Increased strength for better vertical separation

        // Add a forceX to position nodes horizontally based on sibling relationships
        simulation.force('x', d3.forceX<ForceNode>().x(node => {
            // Get the node's depth
            const depth = node.depth || 0;

            // Define horizontal spacing constant
            const SIBLING_SPACING = 150; // Space between siblings

            // For root nodes, use pre-calculated position
            if (depth === 0) {
                return node.x || 0;
            }

            // For child nodes, find siblings
            const parentId = node.parentId;
            if (!parentId) return node.x || 0;

            // Get parent node
            const parentNode = nodeMap.get(parentId);
            if (!parentNode || parentNode.x === undefined) return node.x || 0;

            // Get siblings from the pre-calculated map
            const siblings = parentToSiblingsMap.get(parentId) || [];

            // If this is the only child, center it under the parent
            if (siblings.length === 1) {
                return parentNode.x;
            }

            // Sort siblings by their current x position to maintain relative ordering
            const sortedSiblings = [...siblings].sort((a, b) => {
                const aX = a.x || 0;
                const bX = b.x || 0;
                return aX - bX;
            });

            // Find this node's index among sorted siblings
            const siblingIndex = sortedSiblings.indexOf(node);
            if (siblingIndex === -1) return node.x || 0;

            // Calculate total width of all siblings
            const siblingWidth = siblings.reduce((sum, n) => sum + n.width, 0);
            const spacingWidth = (siblings.length - 1) * SIBLING_SPACING;
            const totalWidth = siblingWidth + spacingWidth;

            // Calculate cumulative width of preceding siblings plus spacing
            let offsetX = 0;
            for (let i = 0; i < siblingIndex; i++) {
                offsetX += sortedSiblings[i].width + SIBLING_SPACING;
            }

            // Position horizontally based on sibling order
            // Center the entire sibling group under the parent
            const siblingGroupStart = parentNode.x - (totalWidth / 2);
            const horizontalPosition = siblingGroupStart + offsetX + (node.width / 2);

            // Enhanced logging for debugging (reduced to avoid log spam)
            if (Math.random() < 0.05) { // Only log 5% of calculations
                console.log(`[SIBLING_SPACING] Positioning node ${node.id} horizontally:`);
                console.log(`[SIBLING_SPACING] - Parent: ${parentId} at x=${parentNode.x}`);
                console.log(`[SIBLING_SPACING] - Sibling index: ${siblingIndex}/${siblings.length}`);
                console.log(`[SIBLING_SPACING] - Horizontal position: ${horizontalPosition}`);
            }

            return horizontalPosition;
        }).strength(0.7)); // Increased strength for better horizontal positioning
    }

    // 5. Center Force (keep everything centered)
    simulation.force('center', d3.forceCenter(centerX, centerY).strength(0.05));

    // --- Track simulation progress and iteration results ---
    let lastUpdate = Date.now();
    let iteration = 0;
    let lastPositions: { [id: string]: { x: number, y: number } } = {};
    let stabilityCount = 0; // Counter for consecutive stable iterations
    const maxStabilityCount = 5; // Number of stable iterations needed to consider simulation done

    // Collect initial positions to track changes
    forceNodes.forEach(node => {
        if (node.x !== undefined && node.y !== undefined) {
            lastPositions[node.id] = { x: node.x, y: node.y };
        }
    });

    // --- Run the simulation ---


    // Add ticks counters and debug code
    let ticksCount = 0;
    const logInterval = 100;

    // Set up simulation parameters and event handlers
    simulation
        .alpha(1)
        .alphaDecay(1 - Math.pow(0.001, 1 / iterations))
        .velocityDecay(0.4)
        .on("tick", () => {
            ticksCount++;

            // Log progress every logInterval ticks
            if (ticksCount % logInterval === 0) {

            }

            // Enforce parent-child spacing on EVERY tick to ensure proper positioning
            // This is critical to prevent depth-based positioning from overriding our calculations
            if (hierarchyAvailable) {
                enforceParentChildSpacing(forceNodes, nodeMap);
            }
        });

    // Run the simulation for the specified number of iterations
    const simulationInterval = setInterval(() => {
        try {
            // Just in case - sometimes the interval doesn't get cleared
            if (iteration >= iterations) {
                clearInterval(simulationInterval);
                return;
            }

            // Single iteration step
            simulation.tick();
            iteration++;

            // Apply jitter occasionally
            if (Math.random() < 0.1) jitter();

            // Apply parent-child spacing enforcement on EVERY iteration
            // This is critical to ensure parent-child relationships are maintained throughout the simulation
            if (hierarchyAvailable) {
                enforceParentChildSpacing(forceNodes, nodeMap);
            }

            // CRITICAL FIX: Send position updates for dragged nodes after each tick
            // This ensures the main thread gets updated positions during dragging
            const draggingNodes = forceNodes.filter(node => node.isDragging);
            if (draggingNodes.length > 0) {
                console.log(`[DragProtocol] 80. Worker sending position updates for ${draggingNodes.length} dragged nodes`);

                // Send all node positions, not just dragged ones, to ensure consistent state
                const nodePositions = forceNodes.map(node => ({
                    id: node.id,
                    x: validateNumber(node.x, centerX),
                    y: validateNumber(node.y, centerY),
                    isDragging: !!node.isDragging
                }));

                // Send position updates to main thread
                self.postMessage({
                    type: 'positionUpdate',
                    id: id,
                    nodes: nodePositions
                });
            }

            // Track stability by monitoring the magnitude of position changes
            let totalMovement = 0;
            let validNodeCount = 0;

            forceNodes.forEach(node => {
                validatePosition(node, centerX, centerY);

                if (lastPositions[node.id] && node.x !== undefined && node.y !== undefined) {
                    const dx = node.x - lastPositions[node.id].x;
                    const dy = node.y - lastPositions[node.id].y;
                    const movement = Math.sqrt(dx*dx + dy*dy);

                    totalMovement += movement;
                    validNodeCount++;

                    // Update stored position
                    lastPositions[node.id] = { x: node.x, y: node.y };
                }
            });

            // Calculate average movement per node for stability check
            const avgMovement = validNodeCount > 0 ? totalMovement / validNodeCount : 0;

            // Check if movement is minimal (stable)
            const isStable = avgMovement < 0.5; // Threshold for "stability"
            stabilityCount = isStable ? stabilityCount + 1 : 0;

            // Send progress update every 100ms or every 10% of iterations
            const currentTime = Date.now();
            const percentComplete = Math.round((iteration / iterations) * 100);

            if (
                currentTime - lastUpdate > 100 || // Time-based update
                (percentComplete % 10 === 0 && percentComplete > 0) || // Percentage-based update
                stabilityCount === maxStabilityCount // Stability-based update
            ) {
                lastUpdate = currentTime;

                self.postMessage({
                    type: 'progress',
                    id: id,
                    iteration: iteration,
                    totalIterations: iterations,
                    percentComplete: percentComplete,
                    stable: stabilityCount >= maxStabilityCount
                });
            }

            // Early termination if the layout is stable for several consecutive iterations
            if (stabilityCount >= maxStabilityCount) {

                clearInterval(simulationInterval);
                finishSimulation();
            }

            // End when we've reached max iterations
            if (iteration >= iterations) {

                clearInterval(simulationInterval);
                finishSimulation();
            }
        } catch (error) {

            clearInterval(simulationInterval);

            // Report the error
            self.postMessage({
                type: 'error',
                id,
                error: `Error during simulation: ${error}`
            });
        }
    }, 0); // Run as fast as possible within browser constraints

    // Function to finish the simulation and send back results
    function finishSimulation() {
        try {
            // Final validation of all node positions and dimensions
            for (const node of forceNodes) {
                // Validate and fix node dimensions with strict limits
                if (isNaN(node.width) || node.width <= 0 || node.width > 10000) {

                    node.width = 336; // Default width
                }

                if (isNaN(node.height) || node.height <= 0 || node.height > 10000) {

                    node.height = 356; // Default height
                }

                // Validate position
                validatePosition(node, centerX, centerY);
            }

            // Apply final overlap resolution
            resolveRemainingOverlaps(forceNodes, nodeDepthMap);

            // --- Post-process node positions based on parent-child relationships ---


            // Apply deterministic hierarchical positioning as the primary layout mechanism
            // This is the key to handling deep hierarchies and variable node heights properly
            let totalAdjustments = 0;
            const maxRounds = 30; // Multiple rounds for deep hierarchies



            // Create a map to track the bottom edge of each node
            const nodeBottomEdgeMap = new Map<string, number>();

            // First calculate the bottom edge position for all nodes
            for (const node of forceNodes) {
                // Validate node dimensions before calculating bottom edge
                if (isNaN(node.height) || node.height <= 0 || node.height > 10000) {

                    node.height = 356; // Default height
                }

                const bottomEdge = (node.y || 0) + (node.height / 2);
                nodeBottomEdgeMap.set(node.id, bottomEdge);

            }

            // Process in multiple rounds to handle complex hierarchies
            for (let round = 0; round < maxRounds; round++) {
                // First apply parent-child spacing to establish proper vertical positioning
                const adjustments = enforceParentChildSpacing(forceNodes, nodeMap);
                totalAdjustments += adjustments;



                // Update bottom edge map after adjustments
                for (const node of forceNodes) {
                    const bottomEdge = (node.y || 0) + (node.height / 2);
                    nodeBottomEdgeMap.set(node.id, bottomEdge);
                }

                // Apply overlap resolution every few rounds
                if (round % 2 === 0) {

                    resolveRemainingOverlaps(forceNodes, nodeDepthMap);

                    // Update bottom edge map after overlap resolution
                    for (const node of forceNodes) {
                        const bottomEdge = (node.y || 0) + (node.height / 2);
                        nodeBottomEdgeMap.set(node.id, bottomEdge);
                    }
                }

                // Stop if no more adjustments are needed
                if (adjustments === 0) {

                    break;
                }
            }

            // Final overlap resolution pass

            resolveRemainingOverlaps(forceNodes, nodeDepthMap);

            // Update bottom edge map after final overlap resolution
            for (const node of forceNodes) {
                const bottomEdge = (node.y || 0) + (node.height / 2);
                nodeBottomEdgeMap.set(node.id, bottomEdge);
            }

            // Final validation and correction of node positions

            let invalidPositionsFixed = 0;

            for (const node of forceNodes) {
                // Validate position
                if (isNaN(node.x!) || !isFinite(node.x!) || node.x === undefined ||
                    isNaN(node.y!) || !isFinite(node.y!) || node.y === undefined) {



                    // Fix based on parent relationship first, then depth
                    if (node.parentId) {
                        const parent = nodeMap.get(node.parentId);
                        if (parent && parent.x !== undefined && parent.y !== undefined) {
                            // Get parent's bottom edge
                            const parentBottomEdge = nodeBottomEdgeMap.get(parent.id) ||
                                ((parent.y || 0) + (parent.height / 2));

                            // Position below parent's bottom edge with proper spacing
                            node.x = parent.x + (Math.random() - 0.5) * 50; // Slight horizontal offset

                            // Calculate dynamic spacing based on parent height
                            const parentHeightFactor = parent.height / 356; // Normalize against default height
                            const dynamicSpacing = 250 * Math.max(1, parentHeightFactor);

                            // Position based on parent's bottom edge plus dynamic spacing
                            node.y = parentBottomEdge + dynamicSpacing + (node.height / 2);


                        } else {
                            // Fallback to depth-based positioning
                            const depth = node.depth || 0;
                            node.x = (Math.random() - 0.5) * 100;
                            node.y = -400 + (depth * 250);
                        }
                    } else {
                        // Root node default position
                        node.x = (Math.random() - 0.5) * 100;
                        node.y = -400; // Root vertical position
                    }

                    // Update the bottom edge map
                    const newBottomEdge = node.y + (node.height / 2);
                    nodeBottomEdgeMap.set(node.id, newBottomEdge);

                    invalidPositionsFixed++;
                }
            }

            if (invalidPositionsFixed > 0) {

                // Run one more round of hierarchical positioning to ensure consistency
                enforceParentChildSpacing(forceNodes, nodeMap);
                // Final overlap resolution
                resolveRemainingOverlaps(forceNodes, nodeDepthMap);

                // Final update of bottom edge map
                for (const node of forceNodes) {
                    const bottomEdge = (node.y || 0) + (node.height / 2);
                    nodeBottomEdgeMap.set(node.id, bottomEdge);
                }
            }

            // Bottom edge map has already been created and updated above
            // No need to recalculate it here

            // Log the final bottom edges for debugging
            forceNodes.forEach(node => {
                const bottomEdge = nodeBottomEdgeMap.get(node.id) || 0;

            });

            // --- Final validation pass to catch any remaining issues ---
            for (const node of forceNodes) {
                validatePosition(node, centerX, centerY);

                // Clear any fixed positions to allow the main thread full control
                node.fx = undefined;
                node.fy = undefined;
            }

            // --- Log final node dimensions and positions for debugging ---

            forceNodes.forEach(node => {


                // If this is a child node, log the relationship with its parent
                if (node.parentId) {
                    const parentNode = nodeMap.get(node.parentId);
                    if (parentNode) {
                        const parentBottomEdge = (parentNode.y || 0) + (parentNode.height / 2);
                        const childTopEdge = (node.y || 0) - (node.height / 2);
                        const verticalGap = childTopEdge - parentBottomEdge;


                    }
                }
            });

            // --- Prepare Final Results ---
            let fixedInvalidPositions = 0;
            let fixedInvalidDimensions = 0;
            const updatedNodes = forceNodes.map(forceNode => {
                // Double-check position validity one last time
                if (isNaN(forceNode.x!) || isNaN(forceNode.y!) ||
                    !isFinite(forceNode.x!) || !isFinite(forceNode.y!) ||
                    forceNode.x === undefined || forceNode.y === undefined) {

                    // If still invalid, use default center position
                    fixedInvalidPositions++;
                    forceNode.x = centerX;
                    forceNode.y = centerY;

                }

                // Double-check dimension validity one last time with strict limits
                if (isNaN(forceNode.width) || isNaN(forceNode.height) ||
                    !isFinite(forceNode.width) || !isFinite(forceNode.height) ||
                    forceNode.width <= 0 || forceNode.height <= 0 ||
                    forceNode.width > 10000 || forceNode.height > 10000) {

                    fixedInvalidDimensions++;


                    // Use default dimensions based on node type or standard defaults
                    forceNode.width = 336; // Default width
                    forceNode.height = 356; // Default height
                }

                // Return data in the expected format, ensuring positions are valid numbers
                // and dimensions are within reasonable limits
                return {
                    id: forceNode.id,
                    x: validateNumber(forceNode.x, centerX), // Validate x position
                    y: validateNumber(forceNode.y, centerY), // Validate y position
                    width: Math.min(10000, validateNumber(forceNode.width, 336)), // Validate width with upper limit
                    height: Math.min(10000, validateNumber(forceNode.height, 356)) // Validate height with upper limit
                };
            });

            if (fixedInvalidPositions > 0) {

            }

            if (fixedInvalidDimensions > 0) {

            }

            // --- Send Result Back ---
            // Check if we have any dragging nodes
            const draggingNodes = updatedNodes.filter(node => {
                const forceNode = forceNodes.find(fn => fn.id === node.id);
                return forceNode && forceNode.isDragging;
            });

            if (draggingNodes.length > 0) {
                console.log(`[DragProtocol] 85. Worker: Sending final result with ${draggingNodes.length} dragged nodes`);

                // Mark dragged nodes in the result
                const nodesWithDragInfo = updatedNodes.map(node => {
                    const forceNode = forceNodes.find(fn => fn.id === node.id);
                    const isDragging = forceNode && forceNode.isDragging;

                    if (isDragging) {
                        console.log(`[DragProtocol] 86. Worker: Marking node ${node.id} as dragging in final result at position (${node.x}, ${node.y})`);
                    }

                    return {
                        ...node,
                        isDragging: isDragging || false
                    };
                });

                self.postMessage({
                    type: 'result',
                    id,
                    nodes: nodesWithDragInfo
                });
            } else {
                // No dragging nodes, send normal result
                self.postMessage({
                    type: 'result',
                    id,
                    nodes: updatedNodes
                });
            }


        } catch (error) {


            // Report the error
            self.postMessage({
                type: 'error',
                id,
                error: `Error during layout finalization: ${error}`
            });
        }
    }
  } catch (error) {
    console.error('[MVCGraphWorker] Fatal error processing layout request:', error);

    // Report the error
    self.postMessage({
      type: 'error',
      id,
      error: `Fatal error processing layout request: ${error}`
    });
  }
}

// --- Worker Message Handler ---
self.onmessage = (event: MessageEvent) => {
  try {
    const data = event.data;


    // Simple message processing - process layout requests
    if (data && data.type === 'layout') {
      processLayoutRequest(data);
    } else if (data && data.type === 'ping') {
      // Simple ping for testing worker availability
      self.postMessage({ type: 'pong' });
    } else if (data && data.type === 'dragUpdate') {
      // Handle drag update messages
      console.log(`[DragProtocol] 90. Worker received dragUpdate for node ${data.nodeId} at canvas position (${data.position.x.toFixed(2)}, ${data.position.y.toFixed(2)})`);

      // Send an immediate position update back to the main thread
      // This ensures the node position is updated in the node store
      self.postMessage({
        type: 'positionUpdate',
        id: data.id || 'drag-' + Date.now(),
        nodes: [{
          id: data.nodeId,
          x: data.position.x,
          y: data.position.y,
          isDragging: true
        }]
      });

      // Log that we've sent the position update
      console.log(`[DragProtocol] 91. Worker sent position update for node ${data.nodeId} at canvas position (${data.position.x.toFixed(2)}, ${data.position.y.toFixed(2)})`);
    } else {
      // Unknown message type
      const errorMsg = `Unknown message type: ${data?.type || 'undefined'}`;
      console.warn(`[MVCGraphWorker] ${errorMsg}`, data);
      self.postMessage({
        type: 'error',
        id: data?.id || 'unknown',
        error: errorMsg
      });
    }
  } catch (error) {
    const errorMsg = `Error handling message: ${error}`;
    console.error(`[MVCGraphWorker] ${errorMsg}`, error);
    self.postMessage({
      type: 'error',
      id: 'error-' + Date.now(),
      error: errorMsg
    });
  }
};

// Send ready message
self.postMessage({ type: 'ready' });

// Export {} for TypeScript module compatibility
export {};
