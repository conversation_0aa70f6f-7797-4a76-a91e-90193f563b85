/**
 * GraphBridge - Bridge component between old and new architecture
 *
 * This component serves as a bridge between the old GraphContext-based
 * architecture and the new MVC architecture. It allows for a gradual
 * transition by supporting both systems simultaneously.
 */

import React, { useEffect, useState, useRef, useCallback } from 'react';
import './GraphBridge.css';
import { useGraph } from '../context/GraphContext';
import { useCanvas } from '../context/CanvasContext';
import { Canvas } from '../view/components/Canvas';
import { MVCGridBackground } from '../view/components/MVCGridBackground';
import { LayoutProgressIndicator } from '../view/components/LayoutProgressIndicator';
import { ViewInteractionLayer } from '../view/components/ViewInteractionLayer';
import { TransformControls } from './TransformControls';
import { useTransform } from '../context/TransformContext';
// ZoomProvider is now handled at a higher level
import { nodeStore } from '../model/NodeStore';
import { nodeController } from '../controller/NodeController';
import { transformController, TransformEvents } from '../controller/TransformController';
// REMOVED: zoomController import - now only used in ViewInteractionLayer
import { NodeData } from '../model/NodeModel';
import { EdgeData } from '../model/EdgeModel';
import { canvasNodeToNodeData, canvasEdgeToEdgeData, exportFromMVC } from '../utils/migrationUtils';
import { BasicEdgeRenderer } from '../view/components/BasicEdgeRenderer';
import { ZoomIndicator } from '../view/components/ZoomIndicator';
import { useNodeInteraction } from '../context/NodeInteractionContext';

// Extend Window interface to include our custom property
declare global {
  interface Window {
    graphBridgeInitialCentering?: boolean;
    // Use the same type as in MVCPowerManager.tsx
    mvcTransform?: {
      scale: number;
      offset: {
        x: number;
        y: number;
      };
    };
  }
}

// Zoom constants are now managed by ZoomController

interface GraphBridgeProps {
  useLegacy?: boolean;
  width?: string | number;
  height?: string | number;
  onNodeClick?: (nodeId: string) => void;
  onAddChild?: (parentId: string, type?: 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image' | 'title', openImmediately?: boolean) => void;
  visibleNodeIds?: Set<string>;
  backgroundColor?: string;
  gridSize?: number;
  dotColor?: string;
  dotSize?: number;
}

export const GraphBridge: React.FC<GraphBridgeProps> = ({
  // useLegacy = false, // Unused but kept for reference
  // width = '100%', // Unused but kept for reference
  // height = '100%', // Unused but kept for reference
  onNodeClick,
  onAddChild,
  // visibleNodeIds, // Unused but kept for reference
  backgroundColor = '#2d2d2d',
  gridSize = 20,
  dotColor = '#555',
  dotSize = 1
}) => {
  // We're not using useCanvas values anymore as we're using TransformController directly
  useCanvas(); // Keep the hook call to maintain context subscription
  const canvasRef = useRef<HTMLDivElement>(null);
  const { nodes: contextNodes, edges: contextEdges, setNodes: setContextNodes } = useGraph();
  const { setOpenNodeEditorHandler } = useNodeInteraction();
  const [isMigrated, setIsMigrated] = useState(false);

  // Migrate data from GraphContext to NodeStore - always using MVC
  useEffect(() => {
    if (!isMigrated && contextNodes.length > 0) {
      // Convert context nodes to NodeData format using migration utilities
      const nodeData: NodeData[] = contextNodes.map(canvasNodeToNodeData);

      // Convert context edges to EdgeData format using migration utilities
      const edgeData: EdgeData[] = contextEdges.map(canvasEdgeToEdgeData);

      // Load data into the store
      nodeStore.setNodes(nodeData);
      nodeStore.setEdges(edgeData);

      setIsMigrated(true);
    }
  }, [isMigrated, contextNodes, contextEdges]);

  // Sync changes from NodeStore back to GraphContext - always using MVC
  useEffect(() => {
    if (isMigrated) {
      const syncToGraphContext = () => {
        // Get all nodes from NodeStore (not used directly, but triggers a refresh)
        nodeStore.getAllNodes();

        // Convert to GraphContext format using migrationUtils
        const { nodes: graphNodes } = exportFromMVC();

        // Update GraphContext
        setContextNodes(graphNodes as any);
      };

      // Subscribe to NodeStore updates
      const unsubscribe = nodeStore.on('graph:updated', syncToGraphContext);

      return unsubscribe;
    }
  }, [isMigrated, setContextNodes]);

  // Always use MVC architecture - legacy rendering removed
  /* Legacy rendering kept for reference
  if (useLegacy) {
    // Render the legacy graph component
    return (
      <div style={{ width, height }}>
        <div>Legacy Graph Component</div>
      </div>
    );
  }
  */

  // Center view on all nodes - using TransformContext
  const centerViewOnAllNodes = () => {
    const allNodes = nodeStore.getAllNodes();
    if (allNodes.length === 0) {
      console.log('No nodes to center on');
      return;
    }

    console.log(`Centering view on ${allNodes.length} nodes using TransformContext`);

    // Get all node IDs
    const nodeIds = allNodes.map(node => node.id);

    // Use the TransformContext to center on all nodes
    // This will handle all the bounds calculation, scaling, and animation
    transformController.centerOnNodes(nodeIds, {
      duration: 500,
      easing: 'easeInOut',
      padding: 50,
      adjustScale: true,
      minScale: 0.1,
      maxScale: 3.0,
      onComplete: () => {
        console.log('Centering animation completed');
      }
    });
  };

  // Handle node click - using TransformContext
  const handleNodeClick = useCallback((nodeId: string) => {
    // Always center on the node first using TransformContext
    console.log(`Centering view on node ${nodeId} using TransformContext`);

    // Use the TransformContext to center on the node
    // This will handle all the bounds calculation and animation
    transformController.centerOnNode(nodeId, {
      duration: 500,
      easing: 'easeInOut',
      adjustScale: false, // Don't change scale, just center
      onComplete: () => {
        console.log('Node centering animation completed');

        // After centering is complete, call the onNodeClick handler if provided
        if (onNodeClick) {
          console.log(`Calling provided onNodeClick handler for node ${nodeId}`);
          onNodeClick(nodeId);
        }
      }
    });
  }, [onNodeClick]);

  // Handle add child
  const handleAddChild = (parentId: string, type?: 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image' | 'title', openImmediately?: boolean) => {
    if (onAddChild) {
      onAddChild(parentId, type, openImmediately);
    } else {
      // Default behavior - use nodeController
      // The nodeController.addChildNode method has been updated to skip view centering
      // This ensures that adding a child node doesn't reset the view position
      nodeController.addChildNode(parentId, {
        type: type || 'richtext',
        title: type === 'title' ? 'Title Node' : 'New Node',
        dimensions: {
          width: type === 'title' ? 450 : 336,
          height: type === 'title' ? 80 : 356
        }
      });

      // Log that we're preserving the view position
      console.log('[GraphBridge] Added child node without resetting view position');
    }
  };

  // State to track the current transform - synced with TransformController
  const [currentTransform, setCurrentTransform] = useState(transformController.getTransform());

  // State for tracking drag operations
  const isDragging = useRef(false);
  const lastPos = useRef({ x: 0, y: 0 });
  const velocity = useRef({ x: 0, y: 0 });
  const lastMoveTime = useRef(0);
  const rafId = useRef<number | null>(null);

  // Constants for zoom are now managed by ZoomController

  // Constants for inertia - significantly reduced (by 95%)
  const INERTIA_VELOCITY_THRESHOLD = 0.01; // Very low threshold to trigger inertia only when needed
  const INERTIA_MAX_VELOCITY = 1.5; // Greatly reduced max velocity (95% reduction from 30)
  const INERTIA_BOOST = 0.75; // Greatly reduced boost (95% reduction from 15)
  const VELOCITY_SMOOTHING = 0.3; // Less smoothing for more direct control

  // Define the node editor handler outside useEffect to ensure it's stable
  const nodeEditorHandler = useCallback((nodeId: string) => {
    console.log(`[GraphBridge] openNodeEditor called for node: ${nodeId} via NodeInteractionContext`);
    handleNodeClick(nodeId);
  }, [handleNodeClick]);

  // Set the handler immediately on mount
  useEffect(() => {
    console.log('[GraphBridge] Setting node editor handler on mount');
    setOpenNodeEditorHandler(nodeEditorHandler);
  }, [nodeEditorHandler, setOpenNodeEditorHandler]);

  // Listen for transform changes from TransformController
  useEffect(() => {
    // Listen for transform changes
    const handleTransformChanged = () => {
      // Get the latest transform from the controller
      const newTransform = transformController.getTransform();

      // Update local state with the latest transform
      setCurrentTransform(newTransform);

      // Update the global mvcTransform variable for other components to use
      window.mvcTransform = newTransform;

      // Dispatch a custom event for components that need to know about transform changes
      window.dispatchEvent(new CustomEvent('mvc-transform-updated', { detail: newTransform }));

      // Log transform changes for debugging

    };

    // Listen for view refresh events
    const handleViewRefresh = (event: Event) => {
      // Cast to CustomEvent to access detail property
      const customEvent = event as CustomEvent;

      // Check if we should skip view centering
      const skipViewCentering = customEvent?.detail?.skipViewCentering === true;

      if (skipViewCentering) {
        console.log('[GraphBridge] Skipping view centering as requested');
      }

      // Get the latest transform from the controller
      const newTransform = transformController.getTransform();

      // Force a re-render by triggering a state update
      setCurrentTransform(newTransform);

      // Update the global mvcTransform variable
      window.mvcTransform = newTransform;

      // Dispatch a custom event for components that need to know about transform changes
      window.dispatchEvent(new CustomEvent('mvc-transform-updated', { detail: newTransform }));

      // Also force a refresh of the node store
      nodeStore.getAllNodes(); // Just trigger the refresh without storing the result
    };

    // Subscribe to transform events
    const unsubscribeTransform = transformController.on(TransformEvents.TRANSFORM_CHANGED, handleTransformChanged);

    // Listen for view refresh events
    window.addEventListener('mvc-refresh-view', handleViewRefresh);

    // Initialize the global mvcTransform variable with the current transform
    window.mvcTransform = transformController.getTransform();

    return () => {
      // Clean up subscriptions
      unsubscribeTransform();
      window.removeEventListener('mvc-refresh-view', handleViewRefresh);
    };
  }, []);



  // Get all nodes from the store
  const storeNodes = nodeStore.getAllNodes();

  // Center view on all nodes only when the component mounts initially - using enhanced TransformController
  useEffect(() => {
    // Track whether initial centering has been done
    const hasInitialCentering = window.graphBridgeInitialCentering;

    if (storeNodes.length > 0 && !hasInitialCentering) {
      console.log('Initial centering of view on all nodes');
      window.graphBridgeInitialCentering = true;

      // Use the enhanced TransformController to center on all nodes
      // No need for manual transition classes as the animation is handled by the controller
      centerViewOnAllNodes();
    }
  }, [storeNodes.length > 0]); // Only depend on whether there are any nodes, not the exact count

  // Handle mouse down for panning
  const handleMouseDown = (e: React.MouseEvent) => {
    try {
      if (e.button !== 0) return; // Only left click for panning

      isDragging.current = true;
      lastPos.current = { x: e.clientX, y: e.clientY };
      lastMoveTime.current = performance.now();
      velocity.current = { x: 0, y: 0 };

      if (canvasRef.current) {
        canvasRef.current.style.cursor = 'grabbing';
      }
    } catch (error) {
      console.error('Error in handleMouseDown:', error);
    }
  };

  // Handle mouse move for panning with exact 1:1 movement
  const handleMouseMove = (e: React.MouseEvent) => {
    try {
      if (!isDragging.current) return;

      const currentTime = performance.now();
      const timeElapsed = currentTime - lastMoveTime.current;

      // Calculate movement delta - exact 1:1 movement with cursor
      const dx = e.clientX - lastPos.current.x;
      const dy = e.clientY - lastPos.current.y;

      // Skip tiny movements to reduce processing
      if (Math.abs(dx) < 0.5 && Math.abs(dy) < 0.5) return;

      // Calculate velocity based on movement delta and time
      if (timeElapsed > 0) {
        // Use raw values for velocity calculation for more natural inertia
        const currentVelocityX = dx / timeElapsed;
        const currentVelocityY = dy / timeElapsed;

        // Apply weighted average for velocity
        velocity.current.x = VELOCITY_SMOOTHING * currentVelocityX + (1 - VELOCITY_SMOOTHING) * velocity.current.x;
        velocity.current.y = VELOCITY_SMOOTHING * currentVelocityY + (1 - VELOCITY_SMOOTHING) * velocity.current.y;
      }

      // Update mouse position tracking
      lastPos.current = { x: e.clientX, y: e.clientY };
      lastMoveTime.current = currentTime;

      // Cancel any existing animation frame to avoid queuing multiple updates
      if (rafId.current !== null) {
        cancelAnimationFrame(rafId.current);
      }

      // Use requestAnimationFrame for smoother updates
      // This helps avoid jitter by syncing with the browser's render cycle
      rafId.current = requestAnimationFrame(() => {
        try {
          // Update the transform using the TransformController
          // We use pan method to apply the delta directly
          transformController.pan(dx, dy);

          // We don't need to update local state as it will be updated via the event listener

          // We'll only dispatch the event on mouse up to reduce overhead during panning
          // This significantly improves performance while maintaining functionality
        } catch (error) {
          console.error('Error updating transform in handleMouseMove:', error);
        }
      });
    } catch (error) {
      console.error('Error in handleMouseMove:', error);
    }
  };

  // Handle mouse up for panning
  const handleMouseUp = () => {
    try {
      if (!isDragging.current) return;

      isDragging.current = false;

      // No need to dispatch any events - the TransformController has already updated the transform
      // and notified all listeners

      // Apply inertia if velocity is above threshold
      const speed = Math.sqrt(
        velocity.current.x * velocity.current.x +
        velocity.current.y * velocity.current.y
      );

      if (speed > INERTIA_VELOCITY_THRESHOLD) {
        // Scale velocity to create natural feeling inertia
        if (speed > INERTIA_MAX_VELOCITY) {
          const scale = INERTIA_MAX_VELOCITY / speed;
          velocity.current.x *= scale;
          velocity.current.y *= scale;
        }

        // Apply boost to make inertia more noticeable
        velocity.current.x *= INERTIA_BOOST;
        velocity.current.y *= INERTIA_BOOST;

        // Start inertia animation
        applyInertia();
      }

      if (canvasRef.current) {
        canvasRef.current.style.cursor = 'grab';
      }
    } catch (error) {
      console.error('Error in handleMouseUp:', error);

      // Ensure we reset the dragging state even if there's an error
      isDragging.current = false;

      // Reset cursor
      if (canvasRef.current) {
        canvasRef.current.style.cursor = 'grab';
      }
    }
  };

  // Apply inertia animation with natural physics-based deceleration
  const applyInertia = () => {
    try {
      if (rafId.current !== null) {
        cancelAnimationFrame(rafId.current);
      }

      const startTime = performance.now();
      // We don't need to store startVelocity since we're using the current velocity directly
      const startOffset = { ...currentTransform.offset };

      // Track accumulated movement for smoother animation
      let accumulatedDx = 0;
      let accumulatedDy = 0;
      let lastFrameTime = startTime;

      // Constants for natural deceleration - increased friction for less inertia
      const FRICTION = 0.85; // Higher friction (0.85 = 15% reduction per frame)
      const MIN_VELOCITY = 0.01; // Lower velocity threshold to stop animation sooner

      const animate = () => {
        try {
          const currentTime = performance.now();
          const frameDelta = currentTime - lastFrameTime;
          lastFrameTime = currentTime;

          // Limit frame delta to avoid large jumps if browser tab was inactive
          const cappedDelta = Math.min(frameDelta, 100);

          // Calculate current velocity with constant friction factor
          // This creates a more natural feeling deceleration that's consistent regardless of speed
          velocity.current.x *= FRICTION;
          velocity.current.y *= FRICTION;

          // Calculate movement delta based on actual frame time
          // This ensures consistent movement regardless of frame rate
          const dx = velocity.current.x * cappedDelta;
          const dy = velocity.current.y * cappedDelta;

          // Accumulate movement
          accumulatedDx += dx;
          accumulatedDy += dy;

          // Calculate new position
          const newX = startOffset.x + accumulatedDx;
          const newY = startOffset.y + accumulatedDy;

          // Update the transform using the TransformController
          transformController.setOffset({
            x: newX,
            y: newY
          });

          // Continue animation if velocity is above threshold
          const speed = Math.sqrt(
            velocity.current.x * velocity.current.x +
            velocity.current.y * velocity.current.y
          );

          if (speed > MIN_VELOCITY) {
            rafId.current = requestAnimationFrame(animate);
          } else {
            // Ensure we end with a clean state
            rafId.current = null;
          }
        } catch (error) {
          console.error('Error in inertia animation frame:', error);
          // Ensure we clean up on error
          if (rafId.current !== null) {
            cancelAnimationFrame(rafId.current);
            rafId.current = null;
          }
        }
      };

      // Start the animation
      rafId.current = requestAnimationFrame(animate);
    } catch (error) {
      console.error('Error starting inertia animation:', error);
      // Ensure we clean up on error
      if (rafId.current !== null) {
        cancelAnimationFrame(rafId.current);
        rafId.current = null;
      }
    }
  };

  // Node opening is now handled by ZoomController

  // REMOVED: Zoom accumulation is now handled by ZoomController

  // Zoom thresholds are now managed by ZoomController

  // Device detection is now handled by ZoomController

  // Finding closest node is now handled by ZoomController

  // REMOVED: Wheel event handling is now centralized in ViewInteractionLayer
  // This comment is kept for documentation purposes

  // Clean up animation frames on unmount
  useEffect(() => {
    // Set initial cursor style
    if (canvasRef.current) {
      canvasRef.current.style.cursor = 'grab';
    }

    // REMOVED: Wheel event listener - now centralized in ViewInteractionLayer

    return () => {
      // Clean up animation frames
      if (rafId.current !== null) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, []);

  // Render the Canvas component directly
  return (
    <div
      className={`graph-container ${isDragging.current ? 'panning' : ''}`}
      ref={canvasRef}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      /* REMOVED: onWheel handler - now centralized in ViewInteractionLayer */
      style={{ position: 'relative', width: '100%', height: '100%', overflow: 'visible' }}
    >
      {/* Grid Background */}
      <MVCGridBackground
        gridSize={gridSize}
        dotColor={dotColor}
        dotSize={dotSize}
      />

      {/* Basic Canvas container for edge rendering */}
      <BasicEdgeRenderer
        nodeStore={nodeStore}
        key="basic-edge-renderer" // Force remount when needed
      />

      {/* Debug overlay - only shown in development */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'absolute',
          bottom: 10,
          right: 10,
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '5px 8px',
          borderRadius: '4px',
          fontSize: '12px',
          fontFamily: 'monospace',
          zIndex: 9999
        }}>
          Scale: {currentTransform.scale.toFixed(2)} | Offset: {Math.round(currentTransform.offset.x)}, {Math.round(currentTransform.offset.y)}
          <br />
          <small>(Using local state - TransformContext available in child components)</small>
        </div>
      )}

      <Canvas
        backgroundColor={backgroundColor}
        onNodeClick={handleNodeClick}
        onAddChild={handleAddChild}
        debug={true}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 2
        }}
      />

      {/* Layout progress indicator */}
      <LayoutProgressIndicator />

      {/* View Interaction Layer for centralized event handling */}
      <ViewInteractionLayer />

      {/* Transform Controls - disabled for production */}
      {/* {process.env.NODE_ENV === 'development' && (
        <TransformControls />
      )} */}

      {/* Zoom Indicator - disabled for production */}
      {/* <ZoomIndicator /> */}
    </div>
  );
};
